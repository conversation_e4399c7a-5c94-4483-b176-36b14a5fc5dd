"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/login/route";
exports.ids = ["app/api/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_login_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/login/route.ts */ \"(rsc)/./app/api/login/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/login/route\",\n        pathname: \"/api/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/login/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/api/login/route.ts\",\n    nextConfigOutput,\n    userland: _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_login_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/login/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/login/route.ts":
/*!********************************!*\
  !*** ./app/api/login/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { name, password } = await request.json();\n        if (!name || !password) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Name and password are required\"\n            }, {\n                status: 400\n            });\n        }\n        const user = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_2__.getUserWithRole)(name);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        const isValidPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compareSync(password, user.password);\n        if (!isValidPassword) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Invalid password\"\n            }, {\n                status: 401\n            });\n        }\n        // Remove password from response\n        const { password: _, ...userWithoutPassword } = user;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRole: () => (/* binding */ assignRole),\n/* harmony export */   assignRolesToNonAdmins: () => (/* binding */ assignRolesToNonAdmins),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   clearNonAdminRoles: () => (/* binding */ clearNonAdminRoles),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllUsersWithRoles: () => (/* binding */ getAllUsersWithRoles),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getNonAdminUserCount: () => (/* binding */ getNonAdminUserCount),\n/* harmony export */   getRoleStats: () => (/* binding */ getRoleStats),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"poy.db\");\nlet db;\nfunction migrateUsersTable(database) {\n    database.serialize(()=>{\n        // Create new table with correct constraints\n        database.run(`\n      CREATE TABLE users_new (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL UNIQUE,\n        email TEXT UNIQUE,\n        phone TEXT,\n        about TEXT,\n        selfie TEXT,\n        password TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Copy data from old table, handling potential duplicates\n        database.run(`\n      INSERT OR IGNORE INTO users_new (id, name, email, phone, about, selfie, password, created_at)\n      SELECT id, name, email, phone, about, selfie, password, created_at FROM users\n    `);\n        // Drop old table\n        database.run(\"DROP TABLE users\");\n        // Rename new table\n        database.run(\"ALTER TABLE users_new RENAME TO users\");\n        console.log(\"✅ Users table migration completed\");\n    });\n}\nfunction initDatabase() {\n    return new Promise((resolve, reject)=>{\n        if (db) {\n            resolve(db);\n            return;\n        }\n        db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n            if (err) {\n                reject(err);\n                return;\n            }\n            // Create tables\n            db.serialize(()=>{\n                // Check if we need to migrate the users table\n                db.get(\"SELECT sql FROM sqlite_master WHERE type='table' AND name='users'\", (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking table schema:\", err);\n                        return;\n                    }\n                    // If table exists but doesn't have UNIQUE constraint on name, migrate it\n                    if (row && row.sql && !row.sql.includes(\"name TEXT NOT NULL UNIQUE\")) {\n                        console.log(\"Migrating users table to add UNIQUE constraint on name...\");\n                        migrateUsersTable(db);\n                    } else {\n                        // Create users table with proper constraints\n                        db.run(`\n              CREATE TABLE IF NOT EXISTS users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                name TEXT NOT NULL UNIQUE,\n                email TEXT UNIQUE,\n                phone TEXT,\n                about TEXT,\n                selfie TEXT,\n                password TEXT NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n              )\n            `);\n                    }\n                });\n                // Create roles table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS roles (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),\n            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create admin user if it doesn't exist and ensure admin role\n                db.get(\"SELECT id FROM users WHERE name = ?\", [\n                    \"admin\"\n                ], (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking admin user:\", err);\n                        return;\n                    }\n                    if (!row) {\n                        // Create admin user\n                        const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"zxcvbnm\", 10);\n                        db.run(\"INSERT INTO users (name, password) VALUES (?, ?)\", [\n                            \"admin\",\n                            hashedPassword\n                        ], function(err) {\n                            if (err) {\n                                console.error(\"Error creating admin user:\", err);\n                                return;\n                            }\n                            // Assign admin role\n                            db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                this.lastID,\n                                \"admin\"\n                            ]);\n                            console.log(\"Admin user created with password: zxcvbnm\");\n                        });\n                    } else {\n                        // Admin user exists, ensure they have admin role\n                        db.get(\"SELECT id FROM roles WHERE user_id = ? AND role = ?\", [\n                            row.id,\n                            \"admin\"\n                        ], (err, roleRow)=>{\n                            if (err) {\n                                console.error(\"Error checking admin role:\", err);\n                                return;\n                            }\n                            if (!roleRow) {\n                                // Remove any existing role and assign admin role\n                                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                                    row.id\n                                ], (err)=>{\n                                    if (err) {\n                                        console.error(\"Error removing existing admin roles:\", err);\n                                        return;\n                                    }\n                                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                        row.id,\n                                        \"admin\"\n                                    ], (err)=>{\n                                        if (err) {\n                                            console.error(\"Error assigning admin role:\", err);\n                                        } else {\n                                            console.log(\"Admin role assigned to existing admin user\");\n                                        }\n                                    });\n                                });\n                            }\n                        });\n                    }\n                });\n                resolve(db);\n            });\n        });\n    });\n}\nfunction getDatabase() {\n    if (db) {\n        return Promise.resolve(db);\n    }\n    return initDatabase();\n}\nfunction closeDatabase() {\n    if (db) {\n        db.close();\n    }\n}\n// User functions\nfunction createUser(name, email, phone, about, selfie) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"password123\", 10) // Default password for new users\n            ;\n            db.run(\"INSERT INTO users (name, email, phone, about, selfie, password) VALUES (?, ?, ?, ?, ?, ?)\", [\n                name,\n                email,\n                phone,\n                about,\n                selfie,\n                hashedPassword\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve({\n                    lastID: this.lastID\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByName(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE name = ?\", [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsers() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(\"SELECT * FROM users ORDER BY name\", (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserWithRole(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE u.name = ?\n      `, [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsersWithRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Role functions\nfunction assignRole(userId, role) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                // Remove existing role\n                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                    userId\n                ], (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    // Assign new role\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        role\n                    ], (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve();\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserRole(userId) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT role FROM roles WHERE user_id = ?\", [\n                userId\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row ? row.role : null);\n            });\n        }).catch(reject);\n    });\n}\nfunction clearNonAdminRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve();\n            });\n        }).catch(reject);\n    });\n}\nfunction getRoleStats() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT role, COUNT(*) as count \n        FROM roles \n        GROUP BY role\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const result = {\n                    detective: 0,\n                    liar: 0,\n                    normal: 0,\n                    admin: 0\n                };\n                rows.forEach((row)=>{\n                    result[row.role] = row.count;\n                });\n                resolve(result);\n            });\n        }).catch(reject);\n    });\n}\nfunction getNonAdminUserCount() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT COUNT(*) as count FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row.count);\n            });\n        }).catch(reject);\n    });\n}\nfunction assignRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users without admin role\n            db.all(`\n        SELECT u.id, u.name FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 3) {\n                    resolve({\n                        message: `Need at least 3 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Clear existing non-admin roles\n                    await clearNonAdminRoles();\n                    // Shuffle users randomly\n                    const shuffled = [\n                        ...rows\n                    ].sort(()=>Math.random() - 0.5);\n                    // Assign exactly 1 detective, 1 liar, and everyone else as normal\n                    await assignRole(shuffled[0].id, \"detective\");\n                    await assignRole(shuffled[1].id, \"liar\");\n                    // Assign normal role to everyone else\n                    for(let i = 2; i < shuffled.length; i++){\n                        await assignRole(shuffled[i].id, \"normal\");\n                    }\n                    resolve({\n                        message: `Roles assigned successfully: 1 detective, 1 liar, ${shuffled.length - 2} normal users`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\n// Clear all data except admin\nfunction clearDatabase() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    db.run('DELETE FROM users WHERE name != \"admin\"', (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve({\n                            message: \"Database cleared successfully\"\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();