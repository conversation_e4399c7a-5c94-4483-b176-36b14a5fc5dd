"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/login/route";
exports.ids = ["app/api/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_login_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/login/route.ts */ \"(rsc)/./app/api/login/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/login/route\",\n        pathname: \"/api/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/login/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/api/login/route.ts\",\n    nextConfigOutput,\n    userland: _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_login_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/login/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/login/route.ts":
/*!********************************!*\
  !*** ./app/api/login/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { name, password } = await request.json();\n        if (!name || !password) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Name and password are required\"\n            }, {\n                status: 400\n            });\n        }\n        const user = (0,_lib_database__WEBPACK_IMPORTED_MODULE_2__.getUserWithRole)(name);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        const isValidPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compareSync(password, user.password);\n        if (!isValidPassword) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Invalid password\"\n            }, {\n                status: 401\n            });\n        }\n        // Remove password from response\n        const { password: _, ...userWithoutPassword } = user;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRole: () => (/* binding */ assignRole),\n/* harmony export */   assignRolesToNonAdmins: () => (/* binding */ assignRolesToNonAdmins),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   clearNonAdminRoles: () => (/* binding */ clearNonAdminRoles),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllUsersWithRoles: () => (/* binding */ getAllUsersWithRoles),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getRoleStats: () => (/* binding */ getRoleStats),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"poy.db\");\nlet db;\nfunction initDatabase() {\n    return new Promise((resolve, reject)=>{\n        if (db) {\n            resolve(db);\n            return;\n        }\n        db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n            if (err) {\n                reject(err);\n                return;\n            }\n            // Create tables\n            db.serialize(()=>{\n                // Create users table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS users (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            name TEXT NOT NULL,\n            email TEXT UNIQUE,\n            phone TEXT,\n            about TEXT,\n            selfie TEXT,\n            password TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n          )\n        `);\n                // Create roles table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS roles (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),\n            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create admin user if it doesn't exist\n                db.get(\"SELECT id FROM users WHERE name = ?\", [\n                    \"admin\"\n                ], (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking admin user:\", err);\n                        return;\n                    }\n                    if (!row) {\n                        const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"zxcvbnm\", 10);\n                        db.run(\"INSERT INTO users (name, password) VALUES (?, ?)\", [\n                            \"admin\",\n                            hashedPassword\n                        ], function(err) {\n                            if (err) {\n                                console.error(\"Error creating admin user:\", err);\n                                return;\n                            }\n                            db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                this.lastID,\n                                \"admin\"\n                            ]);\n                            console.log(\"Admin user created with password: zxcvbnm\");\n                        });\n                    }\n                });\n                resolve(db);\n            });\n        });\n    });\n}\nfunction getDatabase() {\n    if (db) {\n        return Promise.resolve(db);\n    }\n    return initDatabase();\n}\nfunction closeDatabase() {\n    if (db) {\n        db.close();\n    }\n}\n// User functions\nfunction createUser(name, email, phone, about, selfie) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"password123\", 10) // Default password for new users\n            ;\n            db.run(\"INSERT INTO users (name, email, phone, about, selfie, password) VALUES (?, ?, ?, ?, ?, ?)\", [\n                name,\n                email,\n                phone,\n                about,\n                selfie,\n                hashedPassword\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve({\n                    lastID: this.lastID\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByName(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE name = ?\", [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsers() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(\"SELECT * FROM users ORDER BY name\", (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserWithRole(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE u.name = ?\n      `, [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsersWithRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Role functions\nfunction assignRole(userId, role) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                // Remove existing role\n                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                    userId\n                ], (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    // Assign new role\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        role\n                    ], (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve();\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserRole(userId) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT role FROM roles WHERE user_id = ?\", [\n                userId\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row ? row.role : null);\n            });\n        }).catch(reject);\n    });\n}\nfunction clearNonAdminRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve();\n            });\n        }).catch(reject);\n    });\n}\nfunction getRoleStats() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT role, COUNT(*) as count \n        FROM roles \n        GROUP BY role\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const result = {\n                    detective: 0,\n                    liar: 0,\n                    normal: 0,\n                    admin: 0\n                };\n                rows.forEach((row)=>{\n                    result[row.role] = row.count;\n                });\n                resolve(result);\n            });\n        }).catch(reject);\n    });\n}\nfunction assignRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users without admin role\n            db.all(`\n        SELECT u.id FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 3) {\n                    resolve({\n                        message: \"Need at least 3 non-admin users to assign roles\"\n                    });\n                    return;\n                }\n                try {\n                    // Clear existing non-admin roles\n                    await clearNonAdminRoles();\n                    // Shuffle and assign roles\n                    const shuffled = [\n                        ...rows\n                    ].sort(()=>Math.random() - 0.5);\n                    const roles = [\n                        \"detective\",\n                        \"liar\",\n                        \"normal\"\n                    ];\n                    for(let i = 0; i < roles.length; i++){\n                        if (shuffled[i]) {\n                            await assignRole(shuffled[i].id, roles[i]);\n                        }\n                    }\n                    resolve({\n                        message: \"Roles assigned successfully\"\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\n// Clear all data except admin\nfunction clearDatabase() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    db.run('DELETE FROM users WHERE name != \"admin\"', (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve({\n                            message: \"Database cleared successfully\"\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGF0YWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkI7QUFDQTtBQUNOO0FBRXZCLE1BQU1HLFNBQVNELGdEQUFTLENBQUNHLFFBQVFDLEdBQUcsSUFBSTtBQUN4QyxJQUFJQztBQUVHLFNBQVNDO0lBQ2QsT0FBTyxJQUFJQyxRQUFRLENBQUNDLFNBQVNDO1FBQzNCLElBQUlKLElBQUk7WUFDTkcsUUFBUUg7WUFDUjtRQUNGO1FBRUFBLEtBQUssSUFBSVAseURBQWdCLENBQUNHLFFBQVEsQ0FBQ1U7WUFDakMsSUFBSUEsS0FBSztnQkFDUEYsT0FBT0U7Z0JBQ1A7WUFDRjtZQUVBLGdCQUFnQjtZQUNoQk4sR0FBR08sU0FBUyxDQUFDO2dCQUNYLHFCQUFxQjtnQkFDckJQLEdBQUdRLEdBQUcsQ0FBQyxDQUFDOzs7Ozs7Ozs7OztRQVdSLENBQUM7Z0JBRUQscUJBQXFCO2dCQUNyQlIsR0FBR1EsR0FBRyxDQUFDLENBQUM7Ozs7Ozs7O1FBUVIsQ0FBQztnQkFFRCx3Q0FBd0M7Z0JBQ3hDUixHQUFHUyxHQUFHLENBQUMsdUNBQXVDO29CQUFDO2lCQUFRLEVBQUUsQ0FBQ0gsS0FBbUJJO29CQUMzRSxJQUFJSixLQUFLO3dCQUNQSyxRQUFRQyxLQUFLLENBQUMsOEJBQThCTjt3QkFDNUM7b0JBQ0Y7b0JBRUEsSUFBSSxDQUFDSSxLQUFLO3dCQUNSLE1BQU1HLGlCQUFpQm5CLHdEQUFlLENBQUMsV0FBVzt3QkFDbERNLEdBQUdRLEdBQUcsQ0FBQyxvREFBb0Q7NEJBQUM7NEJBQVNLO3lCQUFlLEVBQUUsU0FBa0NQLEdBQWlCOzRCQUN2SSxJQUFJQSxLQUFLO2dDQUNQSyxRQUFRQyxLQUFLLENBQUMsOEJBQThCTjtnQ0FDNUM7NEJBQ0Y7NEJBQ0FOLEdBQUdRLEdBQUcsQ0FBQyxtREFBbUQ7Z0NBQUMsSUFBSSxDQUFDTyxNQUFNO2dDQUFFOzZCQUFROzRCQUNoRkosUUFBUUssR0FBRyxDQUFDO3dCQUNkO29CQUNGO2dCQUNGO2dCQUVBYixRQUFRSDtZQUNWO1FBQ0Y7SUFDRjtBQUNGO0FBRU8sU0FBU2lCO0lBQ2QsSUFBSWpCLElBQUk7UUFDTixPQUFPRSxRQUFRQyxPQUFPLENBQUNIO0lBQ3pCO0lBQ0EsT0FBT0M7QUFDVDtBQUVPLFNBQVNpQjtJQUNkLElBQUlsQixJQUFJO1FBQ05BLEdBQUdtQixLQUFLO0lBQ1Y7QUFDRjtBQUVBLGlCQUFpQjtBQUNWLFNBQVNDLFdBQVdDLElBQVksRUFBRUMsS0FBYSxFQUFFQyxLQUFhLEVBQUVDLEtBQWEsRUFBRUMsTUFBYztJQUNsRyxPQUFPLElBQUl2QixRQUFRLENBQUNDLFNBQVNDO1FBQzNCYSxjQUFjUyxJQUFJLENBQUMxQixDQUFBQTtZQUNqQixNQUFNYSxpQkFBaUJuQix3REFBZSxDQUFDLGVBQWUsSUFBSSxpQ0FBaUM7O1lBQzNGTSxHQUFHUSxHQUFHLENBQUMsNkZBQ0w7Z0JBQUNhO2dCQUFNQztnQkFBT0M7Z0JBQU9DO2dCQUFPQztnQkFBUVo7YUFBZSxFQUNuRCxTQUFrQ1AsR0FBaUI7Z0JBQ2pELElBQUlBLEtBQUs7b0JBQ1BGLE9BQU9FO29CQUNQO2dCQUNGO2dCQUNBSCxRQUFRO29CQUFFWSxRQUFRLElBQUksQ0FBQ0EsTUFBTTtnQkFBQztZQUNoQztRQUVKLEdBQUdZLEtBQUssQ0FBQ3ZCO0lBQ1g7QUFDRjtBQUVPLFNBQVN3QixjQUFjUCxJQUFZO0lBQ3hDLE9BQU8sSUFBSW5CLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0JhLGNBQWNTLElBQUksQ0FBQzFCLENBQUFBO1lBQ2pCQSxHQUFHUyxHQUFHLENBQUMsc0NBQXNDO2dCQUFDWTthQUFLLEVBQUUsQ0FBQ2YsS0FBbUJJO2dCQUN2RSxJQUFJSixLQUFLO29CQUNQRixPQUFPRTtvQkFDUDtnQkFDRjtnQkFDQUgsUUFBUU87WUFDVjtRQUNGLEdBQUdpQixLQUFLLENBQUN2QjtJQUNYO0FBQ0Y7QUFFTyxTQUFTeUI7SUFDZCxPQUFPLElBQUkzQixRQUFRLENBQUNDLFNBQVNDO1FBQzNCYSxjQUFjUyxJQUFJLENBQUMxQixDQUFBQTtZQUNqQkEsR0FBRzhCLEdBQUcsQ0FBQyxxQ0FBcUMsQ0FBQ3hCLEtBQW1CeUI7Z0JBQzlELElBQUl6QixLQUFLO29CQUNQRixPQUFPRTtvQkFDUDtnQkFDRjtnQkFDQUgsUUFBUTRCO1lBQ1Y7UUFDRixHQUFHSixLQUFLLENBQUN2QjtJQUNYO0FBQ0Y7QUFFTyxTQUFTNEIsZ0JBQWdCWCxJQUFZO0lBQzFDLE9BQU8sSUFBSW5CLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0JhLGNBQWNTLElBQUksQ0FBQzFCLENBQUFBO1lBQ2pCQSxHQUFHUyxHQUFHLENBQUMsQ0FBQzs7Ozs7TUFLUixDQUFDLEVBQUU7Z0JBQUNZO2FBQUssRUFBRSxDQUFDZixLQUFtQkk7Z0JBQzdCLElBQUlKLEtBQUs7b0JBQ1BGLE9BQU9FO29CQUNQO2dCQUNGO2dCQUNBSCxRQUFRTztZQUNWO1FBQ0YsR0FBR2lCLEtBQUssQ0FBQ3ZCO0lBQ1g7QUFDRjtBQUVPLFNBQVM2QjtJQUNkLE9BQU8sSUFBSS9CLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0JhLGNBQWNTLElBQUksQ0FBQzFCLENBQUFBO1lBQ2pCQSxHQUFHOEIsR0FBRyxDQUFDLENBQUM7Ozs7O01BS1IsQ0FBQyxFQUFFLENBQUN4QixLQUFtQnlCO2dCQUNyQixJQUFJekIsS0FBSztvQkFDUEYsT0FBT0U7b0JBQ1A7Z0JBQ0Y7Z0JBQ0FILFFBQVE0QjtZQUNWO1FBQ0YsR0FBR0osS0FBSyxDQUFDdkI7SUFDWDtBQUNGO0FBRUEsaUJBQWlCO0FBQ1YsU0FBUzhCLFdBQVdDLE1BQWMsRUFBRUMsSUFBcUM7SUFDOUUsT0FBTyxJQUFJbEMsUUFBUSxDQUFDQyxTQUFTQztRQUMzQmEsY0FBY1MsSUFBSSxDQUFDMUIsQ0FBQUE7WUFDakJBLEdBQUdPLFNBQVMsQ0FBQztnQkFDWCx1QkFBdUI7Z0JBQ3ZCUCxHQUFHUSxHQUFHLENBQUMsdUNBQXVDO29CQUFDMkI7aUJBQU8sRUFBRSxDQUFDN0I7b0JBQ3ZELElBQUlBLEtBQUs7d0JBQ1BGLE9BQU9FO3dCQUNQO29CQUNGO29CQUVBLGtCQUFrQjtvQkFDbEJOLEdBQUdRLEdBQUcsQ0FBQyxtREFBbUQ7d0JBQUMyQjt3QkFBUUM7cUJBQUssRUFBRSxDQUFDOUI7d0JBQ3pFLElBQUlBLEtBQUs7NEJBQ1BGLE9BQU9FOzRCQUNQO3dCQUNGO3dCQUNBSDtvQkFDRjtnQkFDRjtZQUNGO1FBQ0YsR0FBR3dCLEtBQUssQ0FBQ3ZCO0lBQ1g7QUFDRjtBQUVPLFNBQVNpQyxZQUFZRixNQUFjO0lBQ3hDLE9BQU8sSUFBSWpDLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0JhLGNBQWNTLElBQUksQ0FBQzFCLENBQUFBO1lBQ2pCQSxHQUFHUyxHQUFHLENBQUMsNENBQTRDO2dCQUFDMEI7YUFBTyxFQUFFLENBQUM3QixLQUFtQkk7Z0JBQy9FLElBQUlKLEtBQUs7b0JBQ1BGLE9BQU9FO29CQUNQO2dCQUNGO2dCQUNBSCxRQUFRTyxNQUFNQSxJQUFJMEIsSUFBSSxHQUFHO1lBQzNCO1FBQ0YsR0FBR1QsS0FBSyxDQUFDdkI7SUFDWDtBQUNGO0FBRU8sU0FBU2tDO0lBQ2QsT0FBTyxJQUFJcEMsUUFBUSxDQUFDQyxTQUFTQztRQUMzQmEsY0FBY1MsSUFBSSxDQUFDMUIsQ0FBQUE7WUFDakJBLEdBQUdRLEdBQUcsQ0FBQywyQ0FBMkMsQ0FBQ0Y7Z0JBQ2pELElBQUlBLEtBQUs7b0JBQ1BGLE9BQU9FO29CQUNQO2dCQUNGO2dCQUNBSDtZQUNGO1FBQ0YsR0FBR3dCLEtBQUssQ0FBQ3ZCO0lBQ1g7QUFDRjtBQUVPLFNBQVNtQztJQUNkLE9BQU8sSUFBSXJDLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0JhLGNBQWNTLElBQUksQ0FBQzFCLENBQUFBO1lBQ2pCQSxHQUFHOEIsR0FBRyxDQUFDLENBQUM7Ozs7TUFJUixDQUFDLEVBQUUsQ0FBQ3hCLEtBQW1CeUI7Z0JBQ3JCLElBQUl6QixLQUFLO29CQUNQRixPQUFPRTtvQkFDUDtnQkFDRjtnQkFFQSxNQUFNa0MsU0FBUztvQkFBRUMsV0FBVztvQkFBR0MsTUFBTTtvQkFBR0MsUUFBUTtvQkFBR0MsT0FBTztnQkFBRTtnQkFDNURiLEtBQUtjLE9BQU8sQ0FBQyxDQUFDbkM7b0JBQ1o4QixNQUFNLENBQUM5QixJQUFJMEIsSUFBSSxDQUF3QixHQUFHMUIsSUFBSW9DLEtBQUs7Z0JBQ3JEO2dCQUVBM0MsUUFBUXFDO1lBQ1Y7UUFDRixHQUFHYixLQUFLLENBQUN2QjtJQUNYO0FBQ0Y7QUFFTyxTQUFTMkM7SUFDZCxPQUFPLElBQUk3QyxRQUFRLENBQUNDLFNBQVNDO1FBQzNCYSxjQUFjUyxJQUFJLENBQUMxQixDQUFBQTtZQUNqQixtQ0FBbUM7WUFDbkNBLEdBQUc4QixHQUFHLENBQUMsQ0FBQzs7OztNQUlSLENBQUMsRUFBRSxPQUFPeEIsS0FBbUJ5QjtnQkFDM0IsSUFBSXpCLEtBQUs7b0JBQ1BGLE9BQU9FO29CQUNQO2dCQUNGO2dCQUVBLElBQUl5QixLQUFLaUIsTUFBTSxHQUFHLEdBQUc7b0JBQ25CN0MsUUFBUTt3QkFBRThDLFNBQVM7b0JBQWtEO29CQUNyRTtnQkFDRjtnQkFFQSxJQUFJO29CQUNGLGlDQUFpQztvQkFDakMsTUFBTVg7b0JBRU4sMkJBQTJCO29CQUMzQixNQUFNWSxXQUFXOzJCQUFJbkI7cUJBQUssQ0FBQ29CLElBQUksQ0FBQyxJQUFNQyxLQUFLQyxNQUFNLEtBQUs7b0JBQ3RELE1BQU1DLFFBQTZDO3dCQUFDO3dCQUFhO3dCQUFRO3FCQUFTO29CQUVsRixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUQsTUFBTU4sTUFBTSxFQUFFTyxJQUFLO3dCQUNyQyxJQUFJTCxRQUFRLENBQUNLLEVBQUUsRUFBRTs0QkFDZixNQUFNckIsV0FBV2dCLFFBQVEsQ0FBQ0ssRUFBRSxDQUFDQyxFQUFFLEVBQUVGLEtBQUssQ0FBQ0MsRUFBRTt3QkFDM0M7b0JBQ0Y7b0JBRUFwRCxRQUFRO3dCQUFFOEMsU0FBUztvQkFBOEI7Z0JBQ25ELEVBQUUsT0FBT3JDLE9BQU87b0JBQ2RSLE9BQU9RO2dCQUNUO1lBQ0Y7UUFDRixHQUFHZSxLQUFLLENBQUN2QjtJQUNYO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDdkIsU0FBU3FEO0lBQ2QsT0FBTyxJQUFJdkQsUUFBUSxDQUFDQyxTQUFTQztRQUMzQmEsY0FBY1MsSUFBSSxDQUFDMUIsQ0FBQUE7WUFDakJBLEdBQUdPLFNBQVMsQ0FBQztnQkFDWFAsR0FBR1EsR0FBRyxDQUFDLDJDQUEyQyxDQUFDRjtvQkFDakQsSUFBSUEsS0FBSzt3QkFDUEYsT0FBT0U7d0JBQ1A7b0JBQ0Y7b0JBRUFOLEdBQUdRLEdBQUcsQ0FBQywyQ0FBMkMsQ0FBQ0Y7d0JBQ2pELElBQUlBLEtBQUs7NEJBQ1BGLE9BQU9FOzRCQUNQO3dCQUNGO3dCQUNBSCxRQUFROzRCQUFFOEMsU0FBUzt3QkFBZ0M7b0JBQ3JEO2dCQUNGO1lBQ0Y7UUFDRixHQUFHdEIsS0FBSyxDQUFDdkI7SUFDWDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG95LWFwcC8uL2xpYi9kYXRhYmFzZS50cz80ZDQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBzcWxpdGUzIGZyb20gJ3NxbGl0ZTMnXG5pbXBvcnQgYmNyeXB0IGZyb20gJ2JjcnlwdGpzJ1xuaW1wb3J0IHBhdGggZnJvbSAncGF0aCdcblxuY29uc3QgZGJQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdwb3kuZGInKVxubGV0IGRiOiBzcWxpdGUzLkRhdGFiYXNlXG5cbmV4cG9ydCBmdW5jdGlvbiBpbml0RGF0YWJhc2UoKTogUHJvbWlzZTxzcWxpdGUzLkRhdGFiYXNlPiB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgaWYgKGRiKSB7XG4gICAgICByZXNvbHZlKGRiKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgZGIgPSBuZXcgc3FsaXRlMy5EYXRhYmFzZShkYlBhdGgsIChlcnI6IEVycm9yIHwgbnVsbCkgPT4ge1xuICAgICAgaWYgKGVycikge1xuICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIHRhYmxlc1xuICAgICAgZGIuc2VyaWFsaXplKCgpID0+IHtcbiAgICAgICAgLy8gQ3JlYXRlIHVzZXJzIHRhYmxlXG4gICAgICAgIGRiLnJ1bihgXG4gICAgICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgdXNlcnMgKFxuICAgICAgICAgICAgaWQgSU5URUdFUiBQUklNQVJZIEtFWSBBVVRPSU5DUkVNRU5ULFxuICAgICAgICAgICAgbmFtZSBURVhUIE5PVCBOVUxMLFxuICAgICAgICAgICAgZW1haWwgVEVYVCBVTklRVUUsXG4gICAgICAgICAgICBwaG9uZSBURVhULFxuICAgICAgICAgICAgYWJvdXQgVEVYVCxcbiAgICAgICAgICAgIHNlbGZpZSBURVhULFxuICAgICAgICAgICAgcGFzc3dvcmQgVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgICAgIGNyZWF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgICAgIClcbiAgICAgICAgYClcblxuICAgICAgICAvLyBDcmVhdGUgcm9sZXMgdGFibGVcbiAgICAgICAgZGIucnVuKGBcbiAgICAgICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyByb2xlcyAoXG4gICAgICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgICAgICB1c2VyX2lkIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgICAgICByb2xlIFRFWFQgTk9UIE5VTEwgQ0hFQ0sgKHJvbGUgSU4gKCdkZXRlY3RpdmUnLCAnbGlhcicsICdub3JtYWwnLCAnYWRtaW4nKSksXG4gICAgICAgICAgICBhc3NpZ25lZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICAgICAgRk9SRUlHTiBLRVkgKHVzZXJfaWQpIFJFRkVSRU5DRVMgdXNlcnMgKGlkKVxuICAgICAgICAgIClcbiAgICAgICAgYClcblxuICAgICAgICAvLyBDcmVhdGUgYWRtaW4gdXNlciBpZiBpdCBkb2Vzbid0IGV4aXN0XG4gICAgICAgIGRiLmdldCgnU0VMRUNUIGlkIEZST00gdXNlcnMgV0hFUkUgbmFtZSA9ID8nLCBbJ2FkbWluJ10sIChlcnI6IEVycm9yIHwgbnVsbCwgcm93OiBhbnkpID0+IHtcbiAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBhZG1pbiB1c2VyOicsIGVycilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmICghcm93KSB7XG4gICAgICAgICAgICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGJjcnlwdC5oYXNoU3luYygnenhjdmJubScsIDEwKVxuICAgICAgICAgICAgZGIucnVuKCdJTlNFUlQgSU5UTyB1c2VycyAobmFtZSwgcGFzc3dvcmQpIFZBTFVFUyAoPywgPyknLCBbJ2FkbWluJywgaGFzaGVkUGFzc3dvcmRdLCBmdW5jdGlvbih0aGlzOiBzcWxpdGUzLlJ1blJlc3VsdCwgZXJyOiBFcnJvciB8IG51bGwpIHtcbiAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGFkbWluIHVzZXI6JywgZXJyKVxuICAgICAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGRiLnJ1bignSU5TRVJUIElOVE8gcm9sZXMgKHVzZXJfaWQsIHJvbGUpIFZBTFVFUyAoPywgPyknLCBbdGhpcy5sYXN0SUQsICdhZG1pbiddKVxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQWRtaW4gdXNlciBjcmVhdGVkIHdpdGggcGFzc3dvcmQ6IHp4Y3Zibm0nKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9XG4gICAgICAgIH0pXG5cbiAgICAgICAgcmVzb2x2ZShkYilcbiAgICAgIH0pXG4gICAgfSlcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldERhdGFiYXNlKCk6IFByb21pc2U8c3FsaXRlMy5EYXRhYmFzZT4ge1xuICBpZiAoZGIpIHtcbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKGRiKVxuICB9XG4gIHJldHVybiBpbml0RGF0YWJhc2UoKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gY2xvc2VEYXRhYmFzZSgpIHtcbiAgaWYgKGRiKSB7XG4gICAgZGIuY2xvc2UoKVxuICB9XG59XG5cbi8vIFVzZXIgZnVuY3Rpb25zXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlVXNlcihuYW1lOiBzdHJpbmcsIGVtYWlsOiBzdHJpbmcsIHBob25lOiBzdHJpbmcsIGFib3V0OiBzdHJpbmcsIHNlbGZpZTogc3RyaW5nKTogUHJvbWlzZTx7IGxhc3RJRDogbnVtYmVyIH0+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBnZXREYXRhYmFzZSgpLnRoZW4oZGIgPT4ge1xuICAgICAgY29uc3QgaGFzaGVkUGFzc3dvcmQgPSBiY3J5cHQuaGFzaFN5bmMoJ3Bhc3N3b3JkMTIzJywgMTApIC8vIERlZmF1bHQgcGFzc3dvcmQgZm9yIG5ldyB1c2Vyc1xuICAgICAgZGIucnVuKCdJTlNFUlQgSU5UTyB1c2VycyAobmFtZSwgZW1haWwsIHBob25lLCBhYm91dCwgc2VsZmllLCBwYXNzd29yZCkgVkFMVUVTICg/LCA/LCA/LCA/LCA/LCA/KScsIFxuICAgICAgICBbbmFtZSwgZW1haWwsIHBob25lLCBhYm91dCwgc2VsZmllLCBoYXNoZWRQYXNzd29yZF0sIFxuICAgICAgICBmdW5jdGlvbih0aGlzOiBzcWxpdGUzLlJ1blJlc3VsdCwgZXJyOiBFcnJvciB8IG51bGwpIHtcbiAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgfVxuICAgICAgICAgIHJlc29sdmUoeyBsYXN0SUQ6IHRoaXMubGFzdElEIH0pXG4gICAgICAgIH1cbiAgICAgIClcbiAgICB9KS5jYXRjaChyZWplY3QpXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRVc2VyQnlOYW1lKG5hbWU6IHN0cmluZyk6IFByb21pc2U8YW55PiB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgZ2V0RGF0YWJhc2UoKS50aGVuKGRiID0+IHtcbiAgICAgIGRiLmdldCgnU0VMRUNUICogRlJPTSB1c2VycyBXSEVSRSBuYW1lID0gPycsIFtuYW1lXSwgKGVycjogRXJyb3IgfCBudWxsLCByb3c6IGFueSkgPT4ge1xuICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgcmVqZWN0KGVycilcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlKHJvdylcbiAgICAgIH0pXG4gICAgfSkuY2F0Y2gocmVqZWN0KVxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0QWxsVXNlcnMoKTogUHJvbWlzZTxhbnlbXT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGdldERhdGFiYXNlKCkudGhlbihkYiA9PiB7XG4gICAgICBkYi5hbGwoJ1NFTEVDVCAqIEZST00gdXNlcnMgT1JERVIgQlkgbmFtZScsIChlcnI6IEVycm9yIHwgbnVsbCwgcm93czogYW55W10pID0+IHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgIHJlamVjdChlcnIpXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgcmVzb2x2ZShyb3dzKVxuICAgICAgfSlcbiAgICB9KS5jYXRjaChyZWplY3QpXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRVc2VyV2l0aFJvbGUobmFtZTogc3RyaW5nKTogUHJvbWlzZTxhbnk+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBnZXREYXRhYmFzZSgpLnRoZW4oZGIgPT4ge1xuICAgICAgZGIuZ2V0KGBcbiAgICAgICAgU0VMRUNUIHUuKiwgci5yb2xlIFxuICAgICAgICBGUk9NIHVzZXJzIHUgXG4gICAgICAgIExFRlQgSk9JTiByb2xlcyByIE9OIHUuaWQgPSByLnVzZXJfaWQgXG4gICAgICAgIFdIRVJFIHUubmFtZSA9ID9cbiAgICAgIGAsIFtuYW1lXSwgKGVycjogRXJyb3IgfCBudWxsLCByb3c6IGFueSkgPT4ge1xuICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgcmVqZWN0KGVycilcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlKHJvdylcbiAgICAgIH0pXG4gICAgfSkuY2F0Y2gocmVqZWN0KVxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0QWxsVXNlcnNXaXRoUm9sZXMoKTogUHJvbWlzZTxhbnlbXT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGdldERhdGFiYXNlKCkudGhlbihkYiA9PiB7XG4gICAgICBkYi5hbGwoYFxuICAgICAgICBTRUxFQ1QgdS4qLCByLnJvbGUgXG4gICAgICAgIEZST00gdXNlcnMgdSBcbiAgICAgICAgTEVGVCBKT0lOIHJvbGVzIHIgT04gdS5pZCA9IHIudXNlcl9pZCBcbiAgICAgICAgT1JERVIgQlkgdS5uYW1lXG4gICAgICBgLCAoZXJyOiBFcnJvciB8IG51bGwsIHJvd3M6IGFueVtdKSA9PiB7XG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIHJlc29sdmUocm93cylcbiAgICAgIH0pXG4gICAgfSkuY2F0Y2gocmVqZWN0KVxuICB9KVxufVxuXG4vLyBSb2xlIGZ1bmN0aW9uc1xuZXhwb3J0IGZ1bmN0aW9uIGFzc2lnblJvbGUodXNlcklkOiBudW1iZXIsIHJvbGU6ICdkZXRlY3RpdmUnIHwgJ2xpYXInIHwgJ25vcm1hbCcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBnZXREYXRhYmFzZSgpLnRoZW4oZGIgPT4ge1xuICAgICAgZGIuc2VyaWFsaXplKCgpID0+IHtcbiAgICAgICAgLy8gUmVtb3ZlIGV4aXN0aW5nIHJvbGVcbiAgICAgICAgZGIucnVuKCdERUxFVEUgRlJPTSByb2xlcyBXSEVSRSB1c2VyX2lkID0gPycsIFt1c2VySWRdLCAoZXJyOiBFcnJvciB8IG51bGwpID0+IHtcbiAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgfVxuICAgICAgICAgIFxuICAgICAgICAgIC8vIEFzc2lnbiBuZXcgcm9sZVxuICAgICAgICAgIGRiLnJ1bignSU5TRVJUIElOVE8gcm9sZXMgKHVzZXJfaWQsIHJvbGUpIFZBTFVFUyAoPywgPyknLCBbdXNlcklkLCByb2xlXSwgKGVycjogRXJyb3IgfCBudWxsKSA9PiB7XG4gICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgIHJlamVjdChlcnIpXG4gICAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmVzb2x2ZSgpXG4gICAgICAgICAgfSlcbiAgICAgICAgfSlcbiAgICAgIH0pXG4gICAgfSkuY2F0Y2gocmVqZWN0KVxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0VXNlclJvbGUodXNlcklkOiBudW1iZXIpOiBQcm9taXNlPHN0cmluZyB8IG51bGw+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBnZXREYXRhYmFzZSgpLnRoZW4oZGIgPT4ge1xuICAgICAgZGIuZ2V0KCdTRUxFQ1Qgcm9sZSBGUk9NIHJvbGVzIFdIRVJFIHVzZXJfaWQgPSA/JywgW3VzZXJJZF0sIChlcnI6IEVycm9yIHwgbnVsbCwgcm93OiBhbnkpID0+IHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgIHJlamVjdChlcnIpXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgcmVzb2x2ZShyb3cgPyByb3cucm9sZSA6IG51bGwpXG4gICAgICB9KVxuICAgIH0pLmNhdGNoKHJlamVjdClcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNsZWFyTm9uQWRtaW5Sb2xlcygpOiBQcm9taXNlPHZvaWQ+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBnZXREYXRhYmFzZSgpLnRoZW4oZGIgPT4ge1xuICAgICAgZGIucnVuKCdERUxFVEUgRlJPTSByb2xlcyBXSEVSRSByb2xlICE9IFwiYWRtaW5cIicsIChlcnI6IEVycm9yIHwgbnVsbCkgPT4ge1xuICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgcmVqZWN0KGVycilcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlKClcbiAgICAgIH0pXG4gICAgfSkuY2F0Y2gocmVqZWN0KVxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Um9sZVN0YXRzKCk6IFByb21pc2U8eyBba2V5OiBzdHJpbmddOiBudW1iZXIgfT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGdldERhdGFiYXNlKCkudGhlbihkYiA9PiB7XG4gICAgICBkYi5hbGwoYFxuICAgICAgICBTRUxFQ1Qgcm9sZSwgQ09VTlQoKikgYXMgY291bnQgXG4gICAgICAgIEZST00gcm9sZXMgXG4gICAgICAgIEdST1VQIEJZIHJvbGVcbiAgICAgIGAsIChlcnI6IEVycm9yIHwgbnVsbCwgcm93czogYW55W10pID0+IHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgIHJlamVjdChlcnIpXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IHsgZGV0ZWN0aXZlOiAwLCBsaWFyOiAwLCBub3JtYWw6IDAsIGFkbWluOiAwIH1cbiAgICAgICAgcm93cy5mb3JFYWNoKChyb3c6IGFueSkgPT4ge1xuICAgICAgICAgIHJlc3VsdFtyb3cucm9sZSBhcyBrZXlvZiB0eXBlb2YgcmVzdWx0XSA9IHJvdy5jb3VudFxuICAgICAgICB9KVxuICAgICAgICBcbiAgICAgICAgcmVzb2x2ZShyZXN1bHQpXG4gICAgICB9KVxuICAgIH0pLmNhdGNoKHJlamVjdClcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGFzc2lnblJvbGVzVG9Ob25BZG1pbnMoKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgZ2V0RGF0YWJhc2UoKS50aGVuKGRiID0+IHtcbiAgICAgIC8vIEdldCBhbGwgdXNlcnMgd2l0aG91dCBhZG1pbiByb2xlXG4gICAgICBkYi5hbGwoYFxuICAgICAgICBTRUxFQ1QgdS5pZCBGUk9NIHVzZXJzIHUgXG4gICAgICAgIExFRlQgSk9JTiByb2xlcyByIE9OIHUuaWQgPSByLnVzZXJfaWQgXG4gICAgICAgIFdIRVJFIHIucm9sZSBJUyBOVUxMIE9SIHIucm9sZSAhPSAnYWRtaW4nXG4gICAgICBgLCBhc3luYyAoZXJyOiBFcnJvciB8IG51bGwsIHJvd3M6IGFueVtdKSA9PiB7XG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKHJvd3MubGVuZ3RoIDwgMykge1xuICAgICAgICAgIHJlc29sdmUoeyBtZXNzYWdlOiAnTmVlZCBhdCBsZWFzdCAzIG5vbi1hZG1pbiB1c2VycyB0byBhc3NpZ24gcm9sZXMnIH0pXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIENsZWFyIGV4aXN0aW5nIG5vbi1hZG1pbiByb2xlc1xuICAgICAgICAgIGF3YWl0IGNsZWFyTm9uQWRtaW5Sb2xlcygpXG4gICAgICAgICAgXG4gICAgICAgICAgLy8gU2h1ZmZsZSBhbmQgYXNzaWduIHJvbGVzXG4gICAgICAgICAgY29uc3Qgc2h1ZmZsZWQgPSBbLi4ucm93c10uc29ydCgoKSA9PiBNYXRoLnJhbmRvbSgpIC0gMC41KVxuICAgICAgICAgIGNvbnN0IHJvbGVzOiAoJ2RldGVjdGl2ZScgfCAnbGlhcicgfCAnbm9ybWFsJylbXSA9IFsnZGV0ZWN0aXZlJywgJ2xpYXInLCAnbm9ybWFsJ11cbiAgICAgICAgICBcbiAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJvbGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAoc2h1ZmZsZWRbaV0pIHtcbiAgICAgICAgICAgICAgYXdhaXQgYXNzaWduUm9sZShzaHVmZmxlZFtpXS5pZCwgcm9sZXNbaV0pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICAgIFxuICAgICAgICAgIHJlc29sdmUoeyBtZXNzYWdlOiAnUm9sZXMgYXNzaWduZWQgc3VjY2Vzc2Z1bGx5JyB9KVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIHJlamVjdChlcnJvcilcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICB9KS5jYXRjaChyZWplY3QpXG4gIH0pXG59XG5cbi8vIENsZWFyIGFsbCBkYXRhIGV4Y2VwdCBhZG1pblxuZXhwb3J0IGZ1bmN0aW9uIGNsZWFyRGF0YWJhc2UoKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgZ2V0RGF0YWJhc2UoKS50aGVuKGRiID0+IHtcbiAgICAgIGRiLnNlcmlhbGl6ZSgoKSA9PiB7XG4gICAgICAgIGRiLnJ1bignREVMRVRFIEZST00gcm9sZXMgV0hFUkUgcm9sZSAhPSBcImFkbWluXCInLCAoZXJyOiBFcnJvciB8IG51bGwpID0+IHtcbiAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgfVxuICAgICAgICAgIFxuICAgICAgICAgIGRiLnJ1bignREVMRVRFIEZST00gdXNlcnMgV0hFUkUgbmFtZSAhPSBcImFkbWluXCInLCAoZXJyOiBFcnJvciB8IG51bGwpID0+IHtcbiAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgcmVqZWN0KGVycilcbiAgICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXNvbHZlKHsgbWVzc2FnZTogJ0RhdGFiYXNlIGNsZWFyZWQgc3VjY2Vzc2Z1bGx5JyB9KVxuICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgICB9KVxuICAgIH0pLmNhdGNoKHJlamVjdClcbiAgfSlcbn1cbiJdLCJuYW1lcyI6WyJzcWxpdGUzIiwiYmNyeXB0IiwicGF0aCIsImRiUGF0aCIsImpvaW4iLCJwcm9jZXNzIiwiY3dkIiwiZGIiLCJpbml0RGF0YWJhc2UiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsIkRhdGFiYXNlIiwiZXJyIiwic2VyaWFsaXplIiwicnVuIiwiZ2V0Iiwicm93IiwiY29uc29sZSIsImVycm9yIiwiaGFzaGVkUGFzc3dvcmQiLCJoYXNoU3luYyIsImxhc3RJRCIsImxvZyIsImdldERhdGFiYXNlIiwiY2xvc2VEYXRhYmFzZSIsImNsb3NlIiwiY3JlYXRlVXNlciIsIm5hbWUiLCJlbWFpbCIsInBob25lIiwiYWJvdXQiLCJzZWxmaWUiLCJ0aGVuIiwiY2F0Y2giLCJnZXRVc2VyQnlOYW1lIiwiZ2V0QWxsVXNlcnMiLCJhbGwiLCJyb3dzIiwiZ2V0VXNlcldpdGhSb2xlIiwiZ2V0QWxsVXNlcnNXaXRoUm9sZXMiLCJhc3NpZ25Sb2xlIiwidXNlcklkIiwicm9sZSIsImdldFVzZXJSb2xlIiwiY2xlYXJOb25BZG1pblJvbGVzIiwiZ2V0Um9sZVN0YXRzIiwicmVzdWx0IiwiZGV0ZWN0aXZlIiwibGlhciIsIm5vcm1hbCIsImFkbWluIiwiZm9yRWFjaCIsImNvdW50IiwiYXNzaWduUm9sZXNUb05vbkFkbWlucyIsImxlbmd0aCIsIm1lc3NhZ2UiLCJzaHVmZmxlZCIsInNvcnQiLCJNYXRoIiwicmFuZG9tIiwicm9sZXMiLCJpIiwiaWQiLCJjbGVhckRhdGFiYXNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();