"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/assign-roles/route";
exports.ids = ["app/api/admin/assign-roles/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fassign-roles%2Froute&page=%2Fapi%2Fadmin%2Fassign-roles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fassign-roles%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fassign-roles%2Froute&page=%2Fapi%2Fadmin%2Fassign-roles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fassign-roles%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_admin_assign_roles_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/admin/assign-roles/route.ts */ \"(rsc)/./app/api/admin/assign-roles/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/assign-roles/route\",\n        pathname: \"/api/admin/assign-roles\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/assign-roles/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/api/admin/assign-roles/route.ts\",\n    nextConfigOutput,\n    userland: _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_admin_assign_roles_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/assign-roles/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fassign-roles%2Froute&page=%2Fapi%2Fadmin%2Fassign-roles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fassign-roles%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/assign-roles/route.ts":
/*!*********************************************!*\
  !*** ./app/api/admin/assign-roles/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\nasync function POST(request) {\n    try {\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.assignRolesToNonAdmins)();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: result.message\n        });\n    } catch (error) {\n        console.error(\"Assign roles error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to assign roles\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2FkbWluL2Fzc2lnbi1yb2xlcy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUQ7QUFDQTtBQUVoRCxlQUFlRSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNSCxxRUFBc0JBO1FBRTNDLE9BQU9ELGtGQUFZQSxDQUFDSyxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEMsU0FBU0gsT0FBT0csT0FBTztRQUN6QjtJQUVGLEVBQUUsT0FBT0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPUixrRkFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLFNBQVM7UUFBeUIsR0FDcEQ7WUFBRUcsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3ktYXBwLy4vYXBwL2FwaS9hZG1pbi9hc3NpZ24tcm9sZXMvcm91dGUudHM/MmExYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBhc3NpZ25Sb2xlc1RvTm9uQWRtaW5zIH0gZnJvbSAnQC9saWIvZGF0YWJhc2UnXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgYXNzaWduUm9sZXNUb05vbkFkbWlucygpXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6IHJlc3VsdC5tZXNzYWdlXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Fzc2lnbiByb2xlcyBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnRmFpbGVkIHRvIGFzc2lnbiByb2xlcycgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImFzc2lnblJvbGVzVG9Ob25BZG1pbnMiLCJQT1NUIiwicmVxdWVzdCIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSIsInN0YXR1cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/assign-roles/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRole: () => (/* binding */ assignRole),\n/* harmony export */   assignRolesToNonAdmins: () => (/* binding */ assignRolesToNonAdmins),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   clearNonAdminRoles: () => (/* binding */ clearNonAdminRoles),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllUsersWithRoles: () => (/* binding */ getAllUsersWithRoles),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getRoleStats: () => (/* binding */ getRoleStats),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"poy.db\");\nlet db;\nfunction migrateUsersTable(database) {\n    database.serialize(()=>{\n        // Create new table with correct constraints\n        database.run(`\n      CREATE TABLE users_new (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL UNIQUE,\n        email TEXT UNIQUE,\n        phone TEXT,\n        about TEXT,\n        selfie TEXT,\n        password TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Copy data from old table, handling potential duplicates\n        database.run(`\n      INSERT OR IGNORE INTO users_new (id, name, email, phone, about, selfie, password, created_at)\n      SELECT id, name, email, phone, about, selfie, password, created_at FROM users\n    `);\n        // Drop old table\n        database.run(\"DROP TABLE users\");\n        // Rename new table\n        database.run(\"ALTER TABLE users_new RENAME TO users\");\n        console.log(\"✅ Users table migration completed\");\n    });\n}\nfunction initDatabase() {\n    return new Promise((resolve, reject)=>{\n        if (db) {\n            resolve(db);\n            return;\n        }\n        db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n            if (err) {\n                reject(err);\n                return;\n            }\n            // Create tables\n            db.serialize(()=>{\n                // Check if we need to migrate the users table\n                db.get(\"SELECT sql FROM sqlite_master WHERE type='table' AND name='users'\", (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking table schema:\", err);\n                        return;\n                    }\n                    // If table exists but doesn't have UNIQUE constraint on name, migrate it\n                    if (row && row.sql && !row.sql.includes(\"name TEXT NOT NULL UNIQUE\")) {\n                        console.log(\"Migrating users table to add UNIQUE constraint on name...\");\n                        migrateUsersTable(db);\n                    } else {\n                        // Create users table with proper constraints\n                        db.run(`\n              CREATE TABLE IF NOT EXISTS users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                name TEXT NOT NULL UNIQUE,\n                email TEXT UNIQUE,\n                phone TEXT,\n                about TEXT,\n                selfie TEXT,\n                password TEXT NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n              )\n            `);\n                    }\n                });\n                // Create roles table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS roles (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),\n            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create admin user if it doesn't exist\n                db.get(\"SELECT id FROM users WHERE name = ?\", [\n                    \"admin\"\n                ], (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking admin user:\", err);\n                        return;\n                    }\n                    if (!row) {\n                        const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"zxcvbnm\", 10);\n                        db.run(\"INSERT INTO users (name, password) VALUES (?, ?)\", [\n                            \"admin\",\n                            hashedPassword\n                        ], function(err) {\n                            if (err) {\n                                console.error(\"Error creating admin user:\", err);\n                                return;\n                            }\n                            db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                this.lastID,\n                                \"admin\"\n                            ]);\n                            console.log(\"Admin user created with password: zxcvbnm\");\n                        });\n                    }\n                });\n                resolve(db);\n            });\n        });\n    });\n}\nfunction getDatabase() {\n    if (db) {\n        return Promise.resolve(db);\n    }\n    return initDatabase();\n}\nfunction closeDatabase() {\n    if (db) {\n        db.close();\n    }\n}\n// User functions\nfunction createUser(name, email, phone, about, selfie) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"password123\", 10) // Default password for new users\n            ;\n            db.run(\"INSERT INTO users (name, email, phone, about, selfie, password) VALUES (?, ?, ?, ?, ?, ?)\", [\n                name,\n                email,\n                phone,\n                about,\n                selfie,\n                hashedPassword\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve({\n                    lastID: this.lastID\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByName(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE name = ?\", [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsers() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(\"SELECT * FROM users ORDER BY name\", (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserWithRole(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE u.name = ?\n      `, [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsersWithRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Role functions\nfunction assignRole(userId, role) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                // Remove existing role\n                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                    userId\n                ], (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    // Assign new role\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        role\n                    ], (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve();\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserRole(userId) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT role FROM roles WHERE user_id = ?\", [\n                userId\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row ? row.role : null);\n            });\n        }).catch(reject);\n    });\n}\nfunction clearNonAdminRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve();\n            });\n        }).catch(reject);\n    });\n}\nfunction getRoleStats() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT role, COUNT(*) as count \n        FROM roles \n        GROUP BY role\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const result = {\n                    detective: 0,\n                    liar: 0,\n                    normal: 0,\n                    admin: 0\n                };\n                rows.forEach((row)=>{\n                    result[row.role] = row.count;\n                });\n                resolve(result);\n            });\n        }).catch(reject);\n    });\n}\nfunction assignRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users without admin role\n            db.all(`\n        SELECT u.id FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 3) {\n                    resolve({\n                        message: \"Need at least 3 non-admin users to assign roles\"\n                    });\n                    return;\n                }\n                try {\n                    // Clear existing non-admin roles\n                    await clearNonAdminRoles();\n                    // Shuffle and assign roles\n                    const shuffled = [\n                        ...rows\n                    ].sort(()=>Math.random() - 0.5);\n                    const roles = [\n                        \"detective\",\n                        \"liar\",\n                        \"normal\"\n                    ];\n                    for(let i = 0; i < roles.length; i++){\n                        if (shuffled[i]) {\n                            await assignRole(shuffled[i].id, roles[i]);\n                        }\n                    }\n                    resolve({\n                        message: \"Roles assigned successfully\"\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\n// Clear all data except admin\nfunction clearDatabase() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    db.run('DELETE FROM users WHERE name != \"admin\"', (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve({\n                            message: \"Database cleared successfully\"\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fassign-roles%2Froute&page=%2Fapi%2Fadmin%2Fassign-roles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fassign-roles%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();