'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Person {
  id: string
  name: string
  email: string
  phone: string
  about: string
  selfie: string[]
  role: 'detective' | 'liar' | 'normal' | 'admin'
  submissionTime: string
}

export default function People() {
  const [people, setPeople] = useState<Person[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (!userData) {
      router.push('/')
      return
    }

    fetchPeople()
  }, [router])

  const fetchPeople = async () => {
    try {
      const response = await fetch('/api/people')
      if (response.ok) {
        const data = await response.json()
        setPeople(data.people)
      }
    } catch (error) {
      console.error('Failed to fetch people:', error)
    } finally {
      setLoading(false)
    }
  }

  const nextPerson = () => {
    setCurrentIndex((prev) => (prev + 1) % people.length)
  }

  const prevPerson = () => {
    setCurrentIndex((prev) => (prev - 1 + people.length) % people.length)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading people...</div>
      </div>
    )
  }

  if (people.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-xl mb-4">No people found</div>
          <Link href="/dashboard" className="text-indigo-600 hover:text-indigo-800">
            Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  const currentPerson = people[currentIndex]

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'text-purple-600 bg-purple-100'
      case 'detective':
        return 'text-blue-600 bg-blue-100'
      case 'liar':
        return 'text-red-600 bg-red-100'
      case 'normal':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-indigo-600 hover:text-indigo-800 mr-4">
                ← Back
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">All People</h1>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-700">
                {currentIndex + 1} of {people.length}
              </span>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Carousel Controls */}
          <div className="flex justify-center mb-6 space-x-4">
            <button
              onClick={prevPerson}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Previous
            </button>
            <button
              onClick={nextPerson}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Next
            </button>
          </div>

          {/* Person Card */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-gray-900">{currentPerson.name}</h2>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(currentPerson.role)}`}>
                  {currentPerson.role.charAt(0).toUpperCase() + currentPerson.role.slice(1)}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{currentPerson.email || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <p className="mt-1 text-sm text-gray-900">{currentPerson.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Submission Time</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(currentPerson.submissionTime).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">About</label>
                    <p className="mt-1 text-sm text-gray-900">{currentPerson.about || 'No description provided'}</p>
                  </div>
                  {currentPerson.selfie && currentPerson.selfie.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Selfie</label>
                      <div className="mt-1">
                        {currentPerson.selfie.map((url, index) => (
                          <img
                            key={index}
                            src={url}
                            alt={`Selfie ${index + 1}`}
                            className="w-20 h-20 object-cover rounded-md"
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Dots */}
          <div className="flex justify-center mt-6 space-x-2">
            {people.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full ${
                  index === currentIndex ? 'bg-indigo-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
