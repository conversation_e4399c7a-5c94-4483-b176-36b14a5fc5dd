'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Person {
  id: string
  name: string
  email: string
  phone: string
  about: string
  selfie: string[]
  role: 'detective' | 'liar' | 'normal' | 'admin'
  submissionTime: string
}

export default function People() {
  const [people, setPeople] = useState<Person[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (!userData) {
      router.push('/')
      return
    }

    fetchPeople()
  }, [router])

  const fetchPeople = async () => {
    try {
      const response = await fetch('/api/people')
      if (response.ok) {
        const data = await response.json()
        setPeople(data.people)
      }
    } catch (error) {
      console.error('Failed to fetch people:', error)
    } finally {
      setLoading(false)
    }
  }

  const nextPerson = () => {
    setCurrentIndex((prev) => (prev + 1) % people.length)
  }

  const prevPerson = () => {
    setCurrentIndex((prev) => (prev - 1 + people.length) % people.length)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading people...</div>
      </div>
    )
  }

  if (people.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-xl mb-4">No people found</div>
          <Link href="/dashboard" className="text-indigo-600 hover:text-indigo-800">
            Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  const currentPerson = people[currentIndex]

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'text-white bg-gradient-to-r from-purple-600 to-purple-800 shadow-lg'
      case 'detective':
        return 'text-white bg-gradient-to-r from-blue-600 to-blue-800 shadow-lg'
      case 'liar':
        return 'text-white bg-gradient-to-r from-red-600 to-red-800 shadow-lg'
      case 'normal':
        return 'text-white bg-gradient-to-r from-green-600 to-green-800 shadow-lg'
      default:
        return 'text-white bg-gradient-to-r from-gray-600 to-gray-800 shadow-lg'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <nav className="bg-white/80 backdrop-blur-sm shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-indigo-600 hover:text-indigo-800 mr-4 font-semibold">
                ← Back to Dashboard
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Meet Everyone</h1>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-full">
                {currentIndex + 1} of {people.length}
              </span>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Sleek Person Card */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden">
            <div className="p-8">
              {/* Profile Section */}
              <div className="text-center mb-8">
                {currentPerson.selfie && currentPerson.selfie.length > 0 ? (
                  <img
                    src={currentPerson.selfie[0]}
                    alt={`${currentPerson.name}'s photo`}
                    className="w-40 h-40 rounded-full object-cover mx-auto shadow-lg ring-4 ring-white mb-6"
                  />
                ) : (
                  <div className="w-40 h-40 rounded-full bg-gradient-to-br from-gray-300 to-gray-400 mx-auto shadow-lg ring-4 ring-white flex items-center justify-center mb-6">
                    <span className="text-6xl text-white">👤</span>
                  </div>
                )}

                <h2 className="text-4xl font-bold text-gray-900 mb-4">{currentPerson.name}</h2>

                {/* Role Badge */}
                <div className="mb-6">
                  <span className={`px-6 py-3 rounded-full text-lg font-bold ${getRoleColor(currentPerson.role)}`}>
                    {currentPerson.role.charAt(0).toUpperCase() + currentPerson.role.slice(1)}
                  </span>
                </div>
              </div>

              {/* Bio Section */}
              <div className="bg-gray-50 rounded-2xl p-6 mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">About</h3>
                <p className="text-gray-700 text-lg leading-relaxed">
                  {currentPerson.about || "This person hasn't shared anything about themselves yet."}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-center space-x-6 mt-8">
            <button
              onClick={prevPerson}
              className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 flex items-center justify-center text-xl"
            >
              ←
            </button>

            <div className="text-center">
              <div className="text-sm text-gray-500 mb-2">
                {currentIndex + 1} of {people.length}
              </div>
              {/* Dots indicator */}
              <div className="flex space-x-2">
                {people.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-200 ${
                      index === currentIndex
                        ? 'bg-indigo-600 scale-125'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>

            <button
              onClick={nextPerson}
              className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 flex items-center justify-center text-xl"
            >
              →
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}
