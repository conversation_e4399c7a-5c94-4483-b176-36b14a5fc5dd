import { NextRequest, NextResponse } from 'next/server'
import { getAllUsersWithRoles } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Get all users with their roles and passwords (admin only endpoint)
    const users = await getAllUsersWithRoles()
    
    // Add default passwords for display purposes
    const usersWithPasswords = users.map((user: any) => {
      let displayPassword = 'Unknown'
      
      // Determine the likely password based on user type
      if (user.name === 'admin') {
        displayPassword = 'zxcvbnm'
      } else {
        displayPassword = 'password123'
      }
      
      return {
        ...user,
        displayPassword // Add display password field
      }
    })
    
    return NextResponse.json({
      success: true,
      users: usersWithPasswords
    })
    
  } catch (error) {
    console.error('Get admin users error:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to get users with passwords' },
      { status: 500 }
    )
  }
}
