'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: number
  name: string
  email: string | null
  phone: string | null
  about: string | null
  selfie: string | null
  role: 'detective' | 'liar' | 'normal' | 'admin'
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [allUsers, setAllUsers] = useState<User[]>([])
  const [currentUserIndex, setCurrentUserIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user')
    if (!userData) {
      router.push('/')
      return
    }

    const userObj = JSON.parse(userData)
    if (userObj.role === 'admin') {
      router.push('/admin')
      return
    }

    setUser(userObj)
    fetchUsers()
  }, [router])

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/people')
      const data = await response.json()
      
      if (data.success) {
        // Filter out admin users and the current user
        const filteredUsers = data.users.filter((u: User) => 
          u.role !== 'admin' && u.name !== user?.name
        )
        setAllUsers(filteredUsers)
      }
    } catch (error) {
      console.error('Failed to fetch users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user')
    router.push('/')
  }

  const nextUser = () => {
    setCurrentUserIndex((prev) => (prev + 1) % allUsers.length)
  }

  const prevUser = () => {
    setCurrentUserIndex((prev) => (prev - 1 + allUsers.length) % allUsers.length)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) return null

  const currentUser = allUsers[currentUserIndex]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Welcome, {user.name}!</h1>
              <p className="text-gray-600">Your role: <span className="font-semibold capitalize">{user.role}</span></p>
            </div>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Logout
            </button>
          </div>
        </div>

        {/* Carousel */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Other People</h2>
          
          {allUsers.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No other users found.</p>
          ) : (
            <div className="text-center">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900">{currentUser.name}</h3>
                {currentUser.about && (
                  <p className="text-gray-600 mt-2">{currentUser.about}</p>
                )}
                {currentUser.selfie && (
                  <img 
                    src={currentUser.selfie} 
                    alt={`${currentUser.name}'s selfie`}
                    className="mx-auto mt-4 rounded-lg max-w-xs max-h-64 object-cover"
                  />
                )}
              </div>
              
              <div className="flex justify-center space-x-4">
                <button
                  onClick={prevUser}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  Previous
                </button>
                <span className="px-4 py-2 text-gray-600">
                  {currentUserIndex + 1} of {allUsers.length}
                </span>
                <button
                  onClick={nextUser}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
