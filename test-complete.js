// Comprehensive test script for all API endpoints and database operations
// Run with: node test-complete.js
// Make sure the server is running on localhost:3000

const BASE_URL = 'http://localhost:3000'

async function makeRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    return { status: response.status, data, ok: response.ok }
  } catch (error) {
    console.error(`Request failed for ${endpoint}:`, error.message)
    return { status: 0, data: null, ok: false, error: error.message }
  }
}

async function testDatabaseConnection() {
  console.log('\n🔍 Testing Database Connection...')
  
  try {
    // Test getting people (should work even with empty database)
    const result = await makeRequest('/api/people')
    
    if (result.ok) {
      console.log('✅ Database connection successful')
      console.log(`   Found ${result.data.users?.length || 0} users`)
      return true
    } else {
      console.log('❌ Database connection failed:', result.data?.message)
      return false
    }
  } catch (error) {
    console.log('❌ Database connection error:', error.message)
    return false
  }
}

async function testNotionSync() {
  console.log('\n🔄 Testing Notion Sync...')
  
  const result = await makeRequest('/api/sync', { method: 'POST' })
  
  if (result.ok) {
    console.log('✅ Notion sync successful')
    console.log(`   ${result.data.message}`)
    console.log(`   Created users: ${result.data.users?.length || 0}`)
    return result.data.users || []
  } else {
    console.log('❌ Notion sync failed:', result.data?.message)
    console.log('   This might be due to invalid Notion credentials or network issues')
    return []
  }
}

async function testLogin() {
  console.log('\n🔐 Testing Login...')
  
  // Test admin login
  const adminResult = await makeRequest('/api/login', {
    method: 'POST',
    body: JSON.stringify({ name: 'admin', password: 'zxcvbnm' })
  })
  
  if (adminResult.ok) {
    console.log('✅ Admin login successful')
    console.log(`   User: ${adminResult.data.user?.name}, Role: ${adminResult.data.user?.role}`)
  } else {
    console.log('❌ Admin login failed:', adminResult.data?.message)
  }
  
  // Test invalid login
  const invalidResult = await makeRequest('/api/login', {
    method: 'POST',
    body: JSON.stringify({ name: 'nonexistent', password: 'wrong' })
  })
  
  if (!invalidResult.ok && invalidResult.status === 404) {
    console.log('✅ Invalid login properly rejected')
  } else {
    console.log('❌ Invalid login should have been rejected')
  }
  
  return adminResult.ok
}

async function testAdminOperations() {
  console.log('\n⚙️  Testing Admin Operations...')
  
  // Test role assignment
  const assignResult = await makeRequest('/api/admin/assign-roles', { method: 'POST' })
  
  if (assignResult.ok) {
    console.log('✅ Role assignment successful')
    console.log(`   ${assignResult.data.message}`)
  } else {
    console.log('❌ Role assignment failed:', assignResult.data?.message)
  }
  
  // Test database clear
  const clearResult = await makeRequest('/api/admin/clear', { method: 'POST' })
  
  if (clearResult.ok) {
    console.log('✅ Database clear successful')
    console.log(`   ${clearResult.data.message}`)
  } else {
    console.log('❌ Database clear failed:', clearResult.data?.message)
  }
  
  return assignResult.ok && clearResult.ok
}

async function testPeopleEndpoint() {
  console.log('\n👥 Testing People Endpoint...')
  
  const result = await makeRequest('/api/people')
  
  if (result.ok) {
    console.log('✅ People endpoint successful')
    console.log(`   Retrieved ${result.data.users?.length || 0} users`)
    
    // Check that passwords are not included
    const hasPasswords = result.data.users?.some(user => user.password !== undefined)
    if (!hasPasswords) {
      console.log('✅ Passwords properly excluded from response')
    } else {
      console.log('❌ WARNING: Passwords found in response!')
    }
    
    return true
  } else {
    console.log('❌ People endpoint failed:', result.data?.message)
    return false
  }
}

async function runAllTests() {
  console.log('🚀 Starting Comprehensive API Tests...')
  console.log('=' .repeat(50))
  
  const results = {
    database: false,
    people: false,
    login: false,
    admin: false,
    notion: false
  }
  
  // Test database connection first
  results.database = await testDatabaseConnection()
  
  if (!results.database) {
    console.log('\n❌ Database connection failed. Cannot continue with other tests.')
    return results
  }
  
  // Test all endpoints
  results.people = await testPeopleEndpoint()
  results.login = await testLogin()
  results.admin = await testAdminOperations()
  results.notion = (await testNotionSync()).length >= 0 // Consider success if no error
  
  // Summary
  console.log('\n' + '=' .repeat(50))
  console.log('📊 TEST SUMMARY:')
  console.log('=' .repeat(50))
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL'
    console.log(`${status} ${test.toUpperCase()}`)
  })
  
  const passedCount = Object.values(results).filter(Boolean).length
  const totalCount = Object.keys(results).length
  
  console.log(`\n🎯 Overall: ${passedCount}/${totalCount} tests passed`)
  
  if (passedCount === totalCount) {
    console.log('🎉 All tests passed! Your API is working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Check the output above for details.')
  }
  
  return results
}

// Only run if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = { runAllTests, testDatabaseConnection, testNotionSync, testLogin, testAdminOperations, testPeopleEndpoint }
