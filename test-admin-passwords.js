// Test script to verify admin password display functionality
// Run with: node test-admin-passwords.js
// Make sure the server is running on localhost:3001

const BASE_URL = 'http://localhost:3001'

async function makeRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    return { status: response.status, data, ok: response.ok }
  } catch (error) {
    console.error(`Request failed for ${endpoint}:`, error.message)
    return { status: 0, data: null, ok: false, error: error.message }
  }
}

async function testAdminPasswordDisplay() {
  console.log('🔐 Testing Admin Password Display Functionality...')
  console.log('=' .repeat(60))
  
  // Test 1: Compare regular people endpoint vs admin users endpoint
  console.log('\n1. Testing regular people endpoint (should NOT have passwords)...')
  const peopleResult = await makeRequest('/api/people')
  if (peopleResult.ok) {
    console.log('✅ Regular people endpoint successful')
    const hasPasswords = peopleResult.data.users.some(user => 
      user.password !== undefined || user.displayPassword !== undefined
    )
    if (!hasPasswords) {
      console.log('✅ Regular endpoint properly excludes passwords')
    } else {
      console.log('❌ WARNING: Regular endpoint contains password data!')
    }
  } else {
    console.log('❌ Regular people endpoint failed:', peopleResult.data?.message)
  }
  
  // Test 2: Test admin users endpoint (should have display passwords)
  console.log('\n2. Testing admin users endpoint (should have display passwords)...')
  const adminUsersResult = await makeRequest('/api/admin/users')
  if (adminUsersResult.ok) {
    console.log('✅ Admin users endpoint successful')
    console.log(`   Retrieved ${adminUsersResult.data.users.length} users`)
    
    const hasDisplayPasswords = adminUsersResult.data.users.some(user => 
      user.displayPassword !== undefined
    )
    if (hasDisplayPasswords) {
      console.log('✅ Admin endpoint includes display passwords')
    } else {
      console.log('❌ Admin endpoint missing display passwords')
    }
    
    // Check password assignments
    console.log('\n   Password assignments:')
    adminUsersResult.data.users.forEach(user => {
      console.log(`   - ${user.name}: ${user.displayPassword} (Role: ${user.role || 'none'})`)
    })
    
    // Validate password logic
    const adminUser = adminUsersResult.data.users.find(u => u.name === 'admin')
    if (adminUser && adminUser.displayPassword === 'zxcvbnm') {
      console.log('✅ Admin user has correct password (zxcvbnm)')
    } else {
      console.log('❌ Admin user password incorrect')
    }
    
    const nonAdminUsers = adminUsersResult.data.users.filter(u => u.name !== 'admin')
    const allNonAdminHaveCorrectPassword = nonAdminUsers.every(u => u.displayPassword === 'password123')
    if (allNonAdminHaveCorrectPassword && nonAdminUsers.length > 0) {
      console.log('✅ All non-admin users have correct password (password123)')
    } else if (nonAdminUsers.length === 0) {
      console.log('⚠️  No non-admin users found to test')
    } else {
      console.log('❌ Some non-admin users have incorrect passwords')
    }
    
  } else {
    console.log('❌ Admin users endpoint failed:', adminUsersResult.data?.message)
  }
  
  // Test 3: Sync some users and verify passwords
  console.log('\n3. Testing password display after sync...')
  const syncResult = await makeRequest('/api/sync', { method: 'POST' })
  if (syncResult.ok) {
    console.log('✅ Sync successful')
    console.log(`   ${syncResult.data.message}`)
    
    // Check passwords after sync
    const postSyncResult = await makeRequest('/api/admin/users')
    if (postSyncResult.ok) {
      console.log('\n   Post-sync password verification:')
      postSyncResult.data.users.forEach(user => {
        const expectedPassword = user.name === 'admin' ? 'zxcvbnm' : 'password123'
        const isCorrect = user.displayPassword === expectedPassword
        const status = isCorrect ? '✅' : '❌'
        console.log(`   ${status} ${user.name}: ${user.displayPassword} (expected: ${expectedPassword})`)
      })
    }
  } else {
    console.log('❌ Sync failed:', syncResult.data?.message)
  }
  
  console.log('\n' + '=' .repeat(60))
  console.log('🎯 Admin Password Display Test Completed!')
  console.log('\nNOTE: The admin panel at /admin should now display:')
  console.log('- A yellow info box with default passwords')
  console.log('- A password column in the users table')
  console.log('- Copy buttons for each password')
  console.log('- Admin user should show "zxcvbnm"')
  console.log('- All other users should show "password123"')
}

// Only run if this file is executed directly
if (require.main === module) {
  testAdminPasswordDisplay().catch(console.error)
}

module.exports = { testAdminPasswordDisplay }
