// Test script to verify unique constraints on name and email
// Run with: node test-unique-constraints.js
// Make sure the server is running on localhost:3000

const BASE_URL = 'http://localhost:3001'

async function makeRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    return { status: response.status, data, ok: response.ok }
  } catch (error) {
    console.error(`Request failed for ${endpoint}:`, error.message)
    return { status: 0, data: null, ok: false, error: error.message }
  }
}

async function testUniqueConstraints() {
  console.log('🔍 Testing Unique Constraints on Name and Email...')
  console.log('=' .repeat(60))
  
  // First, clear the database to start fresh
  console.log('\n1. Clearing database...')
  const clearResult = await makeRequest('/api/admin/clear', { method: 'POST' })
  if (clearResult.ok) {
    console.log('✅ Database cleared successfully')
  } else {
    console.log('❌ Failed to clear database:', clearResult.data?.message)
    return
  }
  
  // Sync from Notion to get some users
  console.log('\n2. Syncing from Notion...')
  const syncResult = await makeRequest('/api/sync', { method: 'POST' })
  if (syncResult.ok) {
    console.log('✅ Notion sync successful')
    console.log(`   ${syncResult.data.message}`)
    if (syncResult.data.skipped && syncResult.data.skipped.length > 0) {
      console.log('   Skipped users:')
      syncResult.data.skipped.forEach(user => {
        console.log(`   - ${user.name} (${user.email}): ${user.reason}`)
      })
    }
  } else {
    console.log('❌ Notion sync failed:', syncResult.data?.message)
  }
  
  // Try syncing again to test duplicate handling
  console.log('\n3. Testing duplicate handling by syncing again...')
  const syncResult2 = await makeRequest('/api/sync', { method: 'POST' })
  if (syncResult2.ok) {
    console.log('✅ Second sync completed')
    console.log(`   ${syncResult2.data.message}`)
    if (syncResult2.data.skipped && syncResult2.data.skipped.length > 0) {
      console.log('✅ Duplicates properly handled:')
      syncResult2.data.skipped.forEach(user => {
        console.log(`   - ${user.name} (${user.email}): ${user.reason}`)
      })
    } else {
      console.log('⚠️  No duplicates detected - this might indicate an issue')
    }
  } else {
    console.log('❌ Second sync failed:', syncResult2.data?.message)
  }
  
  // Check final user count
  console.log('\n4. Checking final user count...')
  const peopleResult = await makeRequest('/api/people')
  if (peopleResult.ok) {
    console.log('✅ Retrieved user list')
    console.log(`   Total users: ${peopleResult.data.users.length}`)
    
    // Check for unique names and emails
    const names = peopleResult.data.users.map(u => u.name)
    const emails = peopleResult.data.users.map(u => u.email).filter(Boolean)
    
    const uniqueNames = new Set(names)
    const uniqueEmails = new Set(emails)
    
    if (names.length === uniqueNames.size) {
      console.log('✅ All names are unique')
    } else {
      console.log('❌ Duplicate names found!')
    }
    
    if (emails.length === uniqueEmails.size) {
      console.log('✅ All emails are unique')
    } else {
      console.log('❌ Duplicate emails found!')
    }
    
    console.log('\n   Users in database:')
    peopleResult.data.users.forEach(user => {
      console.log(`   - ${user.name} (${user.email || 'no email'}) - Role: ${user.role || 'none'}`)
    })
    
  } else {
    console.log('❌ Failed to retrieve users:', peopleResult.data?.message)
  }
  
  console.log('\n' + '=' .repeat(60))
  console.log('🎯 Unique Constraints Test Completed!')
}

// Only run if this file is executed directly
if (require.main === module) {
  testUniqueConstraints().catch(console.error)
}

module.exports = { testUniqueConstraints }
