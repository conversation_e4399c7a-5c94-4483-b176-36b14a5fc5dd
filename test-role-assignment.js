// Test script to verify automatic role assignment functionality
// Run with: node test-role-assignment.js
// Make sure the server is running on localhost:3001

const BASE_URL = 'http://localhost:3001'

async function makeRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    return { status: response.status, data, ok: response.ok }
  } catch (error) {
    console.error(`Request failed for ${endpoint}:`, error.message)
    return { status: 0, data: null, ok: false, error: error.message }
  }
}

function analyzeRoles(users) {
  const roleCounts = { admin: 0, detective: 0, liar: 0, normal: 0, none: 0 }
  const roleUsers = { admin: [], detective: [], liar: [], normal: [], none: [] }
  
  users.forEach(user => {
    const role = user.role || 'none'
    roleCounts[role]++
    roleUsers[role].push(user.name)
  })
  
  return { roleCounts, roleUsers }
}

async function testRoleAssignment() {
  console.log('🎭 Testing Automatic Role Assignment...')
  console.log('=' .repeat(60))
  
  // Step 1: Clear database
  console.log('\n1. Clearing database...')
  const clearResult = await makeRequest('/api/admin/clear', { method: 'POST' })
  if (clearResult.ok) {
    console.log('✅ Database cleared successfully')
  } else {
    console.log('❌ Failed to clear database:', clearResult.data?.message)
    return
  }
  
  // Step 2: Check initial state (should have admin user)
  console.log('\n2. Checking initial state...')
  const initialResult = await makeRequest('/api/people')
  if (initialResult.ok) {
    const { roleCounts, roleUsers } = analyzeRoles(initialResult.data.users)
    console.log('✅ Initial users retrieved')
    console.log(`   Admin users: ${roleCounts.admin} (${roleUsers.admin.join(', ')})`)
    console.log(`   Total users: ${initialResult.data.users.length}`)
    
    if (roleCounts.admin === 1 && roleUsers.admin.includes('admin')) {
      console.log('✅ Admin user properly has admin role')
    } else {
      console.log('❌ Admin user role issue detected')
    }
  }
  
  // Step 3: Sync from Notion
  console.log('\n3. Syncing from Notion...')
  const syncResult = await makeRequest('/api/sync', { method: 'POST' })
  if (syncResult.ok) {
    console.log('✅ Notion sync successful')
    console.log(`   ${syncResult.data.message}`)
    console.log(`   Non-admin user count: ${syncResult.data.nonAdminUserCount}`)
    console.log(`   Roles automatically assigned: ${syncResult.data.rolesAssigned ? 'Yes' : 'No'}`)
    
    if (syncResult.data.skipped && syncResult.data.skipped.length > 0) {
      console.log('   Skipped users:')
      syncResult.data.skipped.forEach(user => {
        console.log(`   - ${user.name}: ${user.reason}`)
      })
    }
  } else {
    console.log('❌ Notion sync failed:', syncResult.data?.message)
    return
  }
  
  // Step 4: Analyze role distribution
  console.log('\n4. Analyzing role distribution...')
  const peopleResult = await makeRequest('/api/people')
  if (peopleResult.ok) {
    const { roleCounts, roleUsers } = analyzeRoles(peopleResult.data.users)
    
    console.log('✅ Role distribution:')
    console.log(`   Admin: ${roleCounts.admin} users (${roleUsers.admin.join(', ')})`)
    console.log(`   Detective: ${roleCounts.detective} users (${roleUsers.detective.join(', ')})`)
    console.log(`   Liar: ${roleCounts.liar} users (${roleUsers.liar.join(', ')})`)
    console.log(`   Normal: ${roleCounts.normal} users (${roleUsers.normal.join(', ')})`)
    console.log(`   No role: ${roleCounts.none} users (${roleUsers.none.join(', ')})`)
    
    // Validate role assignment rules
    let validationPassed = true
    
    if (roleCounts.admin !== 1) {
      console.log('❌ Should have exactly 1 admin user')
      validationPassed = false
    } else {
      console.log('✅ Exactly 1 admin user')
    }
    
    if (!roleUsers.admin.includes('admin')) {
      console.log('❌ Admin user should have admin role')
      validationPassed = false
    } else {
      console.log('✅ Admin user has admin role')
    }
    
    if (syncResult.data.rolesAssigned) {
      if (roleCounts.detective !== 1) {
        console.log('❌ Should have exactly 1 detective')
        validationPassed = false
      } else {
        console.log('✅ Exactly 1 detective')
      }
      
      if (roleCounts.liar !== 1) {
        console.log('❌ Should have exactly 1 liar')
        validationPassed = false
      } else {
        console.log('✅ Exactly 1 liar')
      }
      
      if (roleCounts.none > 0) {
        console.log('❌ All non-admin users should have roles assigned')
        validationPassed = false
      } else {
        console.log('✅ All non-admin users have roles')
      }
    }
    
    if (validationPassed) {
      console.log('\n🎉 All role assignment rules validated successfully!')
    } else {
      console.log('\n⚠️  Some role assignment rules failed validation')
    }
    
  } else {
    console.log('❌ Failed to retrieve users for analysis')
  }
  
  // Step 5: Test manual role assignment
  console.log('\n5. Testing manual role assignment...')
  const manualAssignResult = await makeRequest('/api/admin/assign-roles', { method: 'POST' })
  if (manualAssignResult.ok) {
    console.log('✅ Manual role assignment successful')
    console.log(`   ${manualAssignResult.data.message}`)
    
    // Check roles after manual assignment
    const finalResult = await makeRequest('/api/people')
    if (finalResult.ok) {
      const { roleCounts } = analyzeRoles(finalResult.data.users)
      console.log('   Final role counts:')
      console.log(`   - Admin: ${roleCounts.admin}, Detective: ${roleCounts.detective}, Liar: ${roleCounts.liar}, Normal: ${roleCounts.normal}`)
    }
  } else {
    console.log('❌ Manual role assignment failed:', manualAssignResult.data?.message)
  }
  
  console.log('\n' + '=' .repeat(60))
  console.log('🎯 Role Assignment Test Completed!')
}

// Only run if this file is executed directly
if (require.main === module) {
  testRoleAssignment().catch(console.error)
}

module.exports = { testRoleAssignment }
